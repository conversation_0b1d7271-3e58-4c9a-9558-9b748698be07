# See https://cloud.google.com/cloud-build/docs/build-config

# this must be specified in seconds. If omitted, defaults to 600s (10 mins)
timeout: 9000s
# this prevents errors if you don't use both _GIT_TAG and _PULL_BASE_REF,
# or any new substitutions added in the future.
options:
  substitution_option: ALLOW_LOOSE
steps:
  # gcb-docker-gcloud is published at https://testgrid.k8s.io/sig-testing-image-pushes#gcb-docker-gcloud
  - name: "gcr.io/k8s-staging-test-infra/gcb-docker-gcloud"
    entrypoint: make
    env:
      - TAG=${_GIT_TAG}
      - PULL_BASE_REF=${_PULL_BASE_REF}
      - PROJECT=${_STAGING_PROJECT}
      - ENABLE_GIT_COMMAND=false
      - DOCKER_CLI_EXPERIMENTAL=enabled
      - DOCKER_BUILDKIT=1
      - IMAGE_REGISTRY=gcr.io/k8s-staging-provider-azure
      - CLOUD_BUILD_IMAGE=ccm
    args:
      - release-staging
substitutions:
  # _GIT_TAG will be filled with a git-based tag for the image, of the form vYYYYMMDD-hash, and
  # can be used as a substitution
  _GIT_TAG: "12345"
  # _PULL_BASE_REF will contain the ref that was pushed to to trigger this build -
  # a branch like 'master' or 'release-0.2', or a tag like 'v0.2'.
  _PULL_BASE_REF: "master"
  # The default gcr.io staging project for Cloud Provider Azure artifacts
  # (=> https://console.cloud.google.com/gcr/images/k8s-staging-provider-azure/GLOBAL).
  _STAGING_PROJECT: "k8s-staging-provider-azure"
  # CLOUD_BUILD_IMAGE decides to build ccm or cnm images.
  _CLOUD_BUILD_IMAGE: "ccm" # FIXME: where did it set to cnm?
